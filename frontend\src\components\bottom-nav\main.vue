<template>
  <nav class="bottom-nav">
    <a
        v-for="(item, index) in navItems"
        :key="index"
        href="#"
        :class="{ active: index === props.activeIndex }"
        @click="handleClick(index, $event)"
    >
      <div class="icon-label-wrapper">
        <i class="fas" :class="item.iconClass"></i>
        <span class="label">{{ item.label }}</span>
      </div>
    </a>
  </nav>
</template>

<script setup>


const props = defineProps({
  navItems: Array,
  activeIndex: Number,
})

const emit = defineEmits(["navigate"]);

const handleClick = (index, e) => {
  e.preventDefault();
  emit("navigate", props.navItems[index].routeName);
};
</script>

<style scoped>

.bottom-nav {
  position: fixed;
  bottom: 2vh;
  width: 75vw;
  height: 7vh;
  left: 50%;
  transform: translateX(-50%);
  border: 1px solid #252e45;
  background-color: #6789de8e;
  border-radius: 100px;
  display: flex;
  flex-wrap: wrap;
  z-index: 1000;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

.bottom-nav a {
  border-radius: 100px;
  width: calc(100% / 3);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "ff-chambers-sans-web", sans-serif;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  flex-direction: column;
}

.bottom-nav a .icon-label-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  height: 100%;
  width: 4rem;
  overflow: hidden;
  border-radius: 50%;
  z-index: 2;
}

.bottom-nav a .fas {
  font-size: 1.8rem;
  margin-top: 1rem;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: inline-block;
  transition: transform 0.3s ease;
}

.bottom-nav a .label {
  font-family: "PT Sans Narrow", serif;
  font-size: 0.9rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-top: 0.2rem;
  color: inherit;
  text-align: center;
  z-index: 999;
}

.bottom-nav a.active .icon-label-wrapper {
  background-color: #fff;
  color: #000;
}

.bottom-nav > a.active .fas {
  color: #44598b !important;
  margin-top: 0.2rem;
}

.bottom-nav a.active .label {
  opacity: 1;
}
</style>