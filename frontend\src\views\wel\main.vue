<template>
  <nav class="bottom-nav">
    <a
        v-for="(item, index) in navItems"
        :key="index"
        href="#"
        :class="{ active: activeIndex === index }"
        @click="handleClick(index, $event)"
    >
      <div class="icon-label-wrapper">
        <i class="fas" :class="item.Class"></i>
        <span class="label">{{ item.label }}</span>
      </div>
    </a>
  </nav>
</template>

<script setup>
import { onMounted, ref } from "vue";

onMounted(() => {
});

const activeIndex = ref(null);
const emit = defineEmits(["navigate"]);

const navItems = [
  { label: "议程", Class: "fa-calendar-alt", routeName: "agenda" },
  { label: "签到", Class: "fa-code", routeName: "checkin" },
  { label: "我的", Class: "fa-user-circle", routeName: "profile" },
];

const handleClick = (index, e) => {
  e.preventDefault();
  if (activeIndex.value === index) {
    return;
  }

  activeIndex.value = index;
  emit("navigate", navItems[index].routeName);
};

</script>

<style scoped>
.bottom-nav {
  position: fixed;
  bottom: 3%;
  width: 75%;
  height: 7%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1a2030;
  border: 1px solid #252e45;
  border-radius: 100px;
  display: flex;
  flex-wrap: wrap;
  z-index: 1000;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

.bottom-nav a {
  border-radius: 100px;
  width: 33.33%;
  padding-top: 1.8%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "ff-chambers-sans-web", sans-serif;
  font-weight: 500;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  flex-direction: column;
}

.bottom-nav a .icon-label-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  height: 4rem;
  width: 4rem;
  overflow: hidden;
  border-radius: 50%;
  z-index: 2;
}

.bottom-nav a .fas {
  font-size: 1.8rem;
  margin-top: 1rem;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: inline-block;
  transition: transform 0.3s ease;
}

.bottom-nav a .label {
  font-family: "PT Sans Narrow", serif;
  font-size: 0.9rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-top: 0.2rem;
  color: inherit;
  text-align: center;
  z-index: 999;
}

.bottom-nav a.active .icon-label-wrapper {
  background-color: #fff;
  transform: translateY(-6px);
  color: #000;
}

.bottom-nav > a.active .fas {
  color: #000 !important;
  margin-top: 0.2rem;
}

.bottom-nav a.active .label {
  opacity: 1;
}
</style>
