<template>
  <div class="simple-test">
    <h1>🧪 简单测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正常工作！</p>
    
    <div class="test-links">
      <h3>测试链接：</h3>
      <ul>
        <li>
        </li>
        <li>
          <router-link to="/dev-tools">🛠️ 开发工具</router-link>
        </li>
        <li>
          <router-link to="/mobile">📲 Mobile页面（会触发登录守卫）</router-link>
        </li>
        <li>
          <router-link to="/dinglogin">🔐 钉钉登录页面</router-link>
        </li>
      </ul>
    </div>
    
    <div class="quick-actions">
      <h3>快速操作：</h3>
      <button @click="simulateLogin" class="btn btn-success">模拟登录</button>
      <button @click="simulateLogout" class="btn btn-warning">模拟登出</button>
      <button @click="checkAuth" class="btn btn-info">检查登录状态</button>
      <button @click="diagnoseAuth" class="btn btn-warning">诊断登录问题</button>
      <button @click="runFullTest" class="btn btn-primary">运行完整测试</button>
      <button @click="loadConsoleTools" class="btn btn-secondary">加载控制台工具</button>
    </div>
    
    <div class="current-status">
      <h3>当前状态：</h3>
      <p>登录状态: <span :class="authStatusClass">{{ authStatusText }}</span></p>
      <p>当前路由: {{ $route.path }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleTest',
  data() {
    return {
      authStatus: false
    }
  },
  computed: {
    authStatusText() {
      return this.authStatus ? '已登录' : '未登录'
    },
    authStatusClass() {
      return this.authStatus ? 'status-success' : 'status-error'
    }
  },
  methods: {
    async simulateLogin() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 使用工具进行模拟登录
        const userInfo = authTool.default.login();

        this.checkAuth();
        alert(`模拟登录成功！\n用户: ${userInfo.user_name}\n账号: ${userInfo.account}`);
      } catch (error) {
        console.error('模拟登录失败:', error);
        alert('模拟登录失败，请检查控制台');
      }
    },

    async simulateLogout() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 使用工具进行登出
        authTool.default.logout();

        this.checkAuth();
        alert('模拟登出成功！');
      } catch (error) {
        console.error('模拟登出失败:', error);
        // 备用方案：直接清除localStorage
        localStorage.removeItem('userInfo');
        this.checkAuth();
        alert('模拟登出成功！');
      }
    },

    async checkAuth() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 使用工具检查认证状态
        const result = authTool.default.check();
        this.authStatus = result.isLoggedIn;

        // 在控制台显示详细信息
        if (result.isLoggedIn && result.userInfo) {
          console.log('当前用户信息:', result.userInfo);
        }
      } catch (error) {
        console.error('检查认证状态失败:', error);
        // 备用方案：使用原来的简单检查
        try {
          const userInfo = localStorage.getItem('userInfo');
          if (userInfo) {
            const parsed = JSON.parse(userInfo);
            this.authStatus = !!(parsed.access_token && parsed.user_id);
          } else {
            this.authStatus = false;
          }
        } catch (e) {
          this.authStatus = false;
        }
      }
    },

    async diagnoseAuth() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 运行诊断
        authTool.default.diagnose();

        alert('诊断完成！请查看控制台输出。');
      } catch (error) {
        console.error('诊断失败:', error);
        alert('诊断失败，请检查控制台');
      }
    },

    async runFullTest() {
      try {
        // 动态导入认证测试工具
        const authTool = await import('@/utils/mobile-auth-test.js');

        // 运行完整测试
        authTool.default.test();

        alert('完整测试已运行，请查看控制台输出');

        // 更新页面状态
        this.checkAuth();
      } catch (error) {
        console.error('运行测试失败:', error);
        alert('运行测试失败，请检查控制台');
      }
    },

    async loadConsoleTools() {
      try {
        // 动态导入认证测试工具
        await import('@/utils/mobile-auth-test.js');

        console.log('✅ 认证测试工具已加载到控制台');
        console.log('💡 可用命令:');
        console.log('- mobileAuthTest.login()     // 模拟登录');
        console.log('- mobileAuthTest.logout()    // 模拟登出');
        console.log('- mobileAuthTest.check()     // 检查登录状态');
        console.log('- mobileAuthTest.diagnose()  // 诊断登录问题');
        console.log('- mobileAuthTest.test()      // 运行完整测试');
        console.log('- mobileAuthTest.help()      // 查看帮助');

        alert('认证测试工具已加载到控制台！\n请打开开发者工具查看可用命令。');
      } catch (error) {
        console.error('加载工具失败:', error);
        alert('加载工具失败，请检查控制台');
      }
    }
  },

  mounted() {
    this.checkAuth();
  }
}
</script>

<style scoped>
.simple-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.test-links, .quick-actions, .current-status {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-links ul {
  list-style: none;
  padding: 0;
}

.test-links li {
  margin: 10px 0;
}

.test-links a {
  color: #007bff;
  text-decoration: none;
  font-size: 16px;
}

.test-links a:hover {
  text-decoration: underline;
}

.btn {
  padding: 10px 20px;
  margin: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-block;
}

.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-info { background: #17a2b8; color: white; }
.btn-primary { background: #007bff; color: white; }
.btn-secondary { background: #6c757d; color: white; }

.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.btn:active {
  transform: translateY(0);
}

.quick-actions {
  text-align: center;
}

.status-success {
  color: #28a745;
  font-weight: bold;
}

.status-error {
  color: #dc3545;
  font-weight: bold;
}

h3 {
  color: #333;
  margin-bottom: 15px;
}
</style>
