/**
 * 移动端认证测试工具
 * 用于开发和调试移动端登录功能
 */

// 创建全局测试对象
window.mobileAuthTest = {
  /**
   * 模拟登录
   */
  login() {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);

    const mockUserInfo = {
      // 基础认证信息
      access_token: 'mock_token_' + timestamp,
      refresh_token: 'mock_refresh_token_' + timestamp,
      user_id: 'test_user_' + randomId,
      user_name: '测试用户',
      account: 'testuser',

      // 钉钉登录特有字段
      source: 'dingtalk',
      authCode: 'mock_auth_code_' + timestamp,
      dingUserId: 'ding_user_' + randomId,

      // 其他用户信息
      avatar: '',
      email: '<EMAIL>',
      phone: '***********',
      loginTime: new Date().toISOString(),

      // Token过期时间（1小时后）
      expires_in: 3600,
      token_expires_at: timestamp + (3600 * 1000),

      // 确保通过路由守卫检查的关键字段
      tenant_id: '000000',
      role_id: 'test_role',
      dept_id: 'test_dept'
    };
    
    localStorage.setItem('userInfo', JSON.stringify(mockUserInfo));
    console.log('✅ 模拟登录成功:', mockUserInfo);
    return mockUserInfo;
  },

  /**
   * 模拟登出
   */
  logout() {
    localStorage.removeItem('userInfo');
    console.log('✅ 模拟登出成功');
    return true;
  },

  /**
   * 检查登录状态
   */
  check() {
    try {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        const parsed = JSON.parse(userInfo);
        const isValid = !!(parsed.access_token && parsed.user_id);

        // 详细的状态检查
        console.log('🔍 登录状态检查:', isValid ? '已登录' : '未登录');
        console.log('📋 用户信息:', parsed);

        // 检查钉钉登录相关字段
        const isDingLogin = parsed.source === 'dingtalk' ||
                           parsed.authCode ||
                           parsed.dingUserId;
        console.log('🔐 钉钉登录检查:', isDingLogin ? '通过' : '失败');

        // 检查token过期时间
        if (parsed.token_expires_at) {
          const now = Date.now();
          const isExpired = now >= parsed.token_expires_at;
          const timeLeft = Math.max(0, parsed.token_expires_at - now);
          console.log('⏰ Token状态:', isExpired ? '已过期' : `剩余${Math.floor(timeLeft/1000/60)}分钟`);
        } else {
          console.log('⚠️ 缺少token过期时间字段');
        }

        return { isLoggedIn: isValid, userInfo: parsed, isDingLogin };
      } else {
        console.log('🔍 登录状态检查: 未登录');
        return { isLoggedIn: false, userInfo: null };
      }
    } catch (e) {
      console.error('❌ 检查登录状态失败:', e);
      return { isLoggedIn: false, userInfo: null, error: e.message };
    }
  },

  /**
   * 运行完整测试
   */
  test() {
    console.log('🧪 开始移动端认证测试...');
    
    // 1. 检查初始状态
    console.log('\n1️⃣ 检查初始状态:');
    this.check();
    
    // 2. 执行登录
    console.log('\n2️⃣ 执行模拟登录:');
    this.login();
    
    // 3. 验证登录状态
    console.log('\n3️⃣ 验证登录状态:');
    this.check();
    
    // 4. 执行登出
    console.log('\n4️⃣ 执行登出:');
    this.logout();
    
    // 5. 验证登出状态
    console.log('\n5️⃣ 验证登出状态:');
    this.check();
    
    console.log('\n✅ 移动端认证测试完成!');
  },

  /**
   * 清理所有数据
   */
  clear() {
    localStorage.removeItem('userInfo');
    console.log('🧹 已清理所有认证数据');
  },

  /**
   * 设置自定义用户信息
   */
  setUser(userInfo) {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);

    const defaultInfo = {
      // 基础认证信息
      access_token: 'custom_token_' + timestamp,
      refresh_token: 'custom_refresh_token_' + timestamp,
      user_id: 'custom_user_' + randomId,
      user_name: '自定义用户',
      account: 'customuser',

      // 钉钉登录特有字段
      source: 'dingtalk',
      authCode: 'custom_auth_code_' + timestamp,
      dingUserId: 'custom_ding_user_' + randomId,

      // 其他信息
      loginTime: new Date().toISOString(),
      expires_in: 3600,
      token_expires_at: timestamp + (3600 * 1000),
      tenant_id: '000000',
      role_id: 'test_role',
      dept_id: 'test_dept'
    };
    
    const finalUserInfo = { ...defaultInfo, ...userInfo };
    localStorage.setItem('userInfo', JSON.stringify(finalUserInfo));
    console.log('✅ 已设置自定义用户信息:', finalUserInfo);
    return finalUserInfo;
  },

  /**
   * 诊断登录问题
   */
  diagnose() {
    console.log('🔧 开始诊断登录问题...\n');

    // 1. 检查localStorage
    const userInfo = localStorage.getItem('userInfo');
    console.log('1️⃣ localStorage检查:');
    console.log('   userInfo存在:', !!userInfo);

    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo);
        console.log('   JSON解析: 成功');
        console.log('   access_token:', !!parsed.access_token);
        console.log('   user_id:', !!parsed.user_id);
        console.log('   source:', parsed.source);
        console.log('   authCode:', !!parsed.authCode);
        console.log('   dingUserId:', !!parsed.dingUserId);
        console.log('   token_expires_at:', parsed.token_expires_at);

        if (parsed.token_expires_at) {
          const now = Date.now();
          const isExpired = now >= parsed.token_expires_at;
          console.log('   token是否过期:', isExpired);
          if (!isExpired) {
            console.log('   剩余时间:', Math.floor((parsed.token_expires_at - now) / 1000 / 60), '分钟');
          }
        }
      } catch (e) {
        console.log('   JSON解析: 失败', e.message);
      }
    }

    // 2. 检查路由守卫逻辑
    console.log('\n2️⃣ 路由守卫检查逻辑模拟:');
    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo);
        const hasToken = !!(parsed.access_token);
        const hasUserId = !!(parsed.user_id);
        const isDingLogin = parsed.source === 'dingtalk' ||
                           parsed.authCode ||
                           parsed.dingUserId ||
                           (hasToken && hasUserId);

        console.log('   hasToken:', hasToken);
        console.log('   hasUserId:', hasUserId);
        console.log('   isDingLogin:', isDingLogin);
        console.log('   最终结果:', hasToken && hasUserId && isDingLogin);
      } catch (e) {
        console.log('   检查失败:', e.message);
      }
    }

    console.log('\n✅ 诊断完成');
  },

  /**
   * 获取帮助信息
   */
  help() {
    console.log(`
🛠️ 移动端认证测试工具帮助

可用命令:
- mobileAuthTest.login()     // 模拟登录
- mobileAuthTest.logout()    // 模拟登出
- mobileAuthTest.check()     // 检查登录状态
- mobileAuthTest.test()      // 运行完整测试
- mobileAuthTest.clear()     // 清理所有数据
- mobileAuthTest.setUser(userInfo) // 设置自定义用户信息
- mobileAuthTest.diagnose()  // 诊断登录问题
- mobileAuthTest.help()      // 显示帮助信息

示例:
mobileAuthTest.setUser({
  user_name: '张三',
  account: 'zhangsan',
  email: '<EMAIL>'
});
    `);
  }
};

console.log('🔧 移动端认证测试工具已加载');
console.log('💡 输入 mobileAuthTest.help() 查看帮助信息');

export default window.mobileAuthTest;
