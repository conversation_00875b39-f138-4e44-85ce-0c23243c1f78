<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <main class="page-content">
      <div class="list-container">
        <!-- 标题区域添加装饰效果 -->
        <div class="form-header">
          <div class="header-decoration"></div>
          <i class="fas fa-calendar-alt"></i>
          <h2>{{ mainTitle || "会议议程" }}</h2>
          <p>{{ subTitle || "数智攀登，管理跃升 - 企业管理现场会详细安排" }}</p>
          <div class="header-decoration"></div>
        </div>

        <!-- 加载状态 -->
        <LoadingIndicator
          :show="isLoading"
          text="正在加载议程数据..."
          size="normal"
        />

        <!-- 错误提示 -->
        <ErrorMessage
          :show="hasError && !isLoading"
          type="warning"
          title="数据加载提示"
          :message="errorMessage"
          :show-retry="true"
          :retrying="isLoading"
          @retry="refreshData"
          @close="clearError"
        />

        <!-- 动态渲染议程数据 - 优化滚动容器 -->
        <div class="agenda-scroll-container">
          <div v-if="agendaList && agendaList.length > 0">
            <!-- 按日期分组显示 -->
            <div
              v-for="(dayAgenda, date) in groupedAgenda"
              :key="date"
              class="day-section"
            >
              <!-- 日期标题添加图标和动画 -->
              <h3 class="day-title">
                <i class="fas fa-calendar-day"></i>
                <span>{{ formatDate(date) }}</span>
                <div class="day-divider"></div>
              </h3>

              <!-- 议程项添加悬停效果和层次感 -->
              <div v-for="item in dayAgenda" :key="item.id" class="list-item">
                <div class="time-badge">{{ item.time }}</div>
                <h3 class="agenda-title">{{ item.topic }}</h3>

                <!-- 详情信息使用网格布局 -->
                <div class="agenda-details">
                  <p v-if="item.description" class="description">
                    <i class="fas fa-info-circle"></i> {{ item.description }}
                  </p>
                  <p v-if="item.speaker" class="speaker">
                    <i class="fas fa-user"></i> {{ item.speaker }}
                  </p>
                  <p v-if="item.venue" class="venue">
                    <i class="fas fa-map-marker-alt"></i> {{ item.venue }}
                  </p>
                </div>

                <!-- 区分不同类型议程的装饰线 -->
                <div
                  class="agenda-divider"
                  :class="item.speaker ? 'has-speaker' : ''"
                ></div>
              </div>
            </div>
          </div>

          <!-- 无数据时显示优化 -->
          <div v-else class="day-section empty-state">
            <h3 class="day-title">
              <i class="fas fa-calendar-day"></i>
              <span>会议议程</span>
            </h3>
            <div class="list-item empty-item">
              <i class="fas fa-calendar-check"></i>
              <p>暂无议程数据，请稍后刷新...</p>
            </div>
          </div>
        </div>

        <!-- 下载区域添加动画效果 -->
        <div class="download-section">
          <button class="submit-btn" @click="downloadAgenda">
            <i class="fas fa-download"></i>
            下载完整议程
          </button>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
// 脚本部分保持不变
import { getList } from "@/api/agenda/agenda";
import apiMixin from "@/mixins/apiMixin";
import { dataTransformers } from "@/utils/apiHelper";
import LoadingIndicator from "@/components/LoadingIndicator.vue";
import ErrorMessage from "@/components/ErrorMessage.vue";
import ApiStatus from "@/components/ApiStatus.vue";
import { downloadFileBlob } from "@/utils/util";
import * as dd from 'dingtalk-jsapi';
import { mobileRequest } from "@/utils/mobileRequest";

export default {
  name: "Agenda",
  mixins: [apiMixin],
  components: {
    LoadingIndicator,
    ErrorMessage,
    ApiStatus,
  },
  data() {
    return {
      mainTitle: "",
      subTitle: "",
      agendaList: [],
      dataSource: "unknown",
      responseTime: 0,
      query: {},
      agendaPdfUrl: "", // 议程PDF的URL，从字典读取
    };
  },
  computed: {
    /**
     * 按日期分组的议程数据，并按时间顺序排序
     */
    groupedAgenda() {
      const grouped = {};

      // 按日期分组
      this.agendaList.forEach((item) => {
        const date = item.date || "2025-09-15";
        if (!grouped[date]) {
          grouped[date] = [];
        }
        grouped[date].push(item);
      });

      // 按时间排序每一天的议程
      Object.keys(grouped).forEach((date) => {
        grouped[date].sort((a, b) => {
          // 提取开始时间进行比较
          const timeA = this.extractStartTime(a.time);
          const timeB = this.extractStartTime(b.time);
          return timeA.localeCompare(timeB);
        });
      });

      // 将分组对象转换为按日期排序的数组
      const sortedDates = Object.keys(grouped).sort((a, b) => {
        // 按日期排序，确保第一天在前
        return new Date(a).getTime() - new Date(b).getTime();
      });

      // 返回按日期排序的对象
      const sortedGrouped = {};
      sortedDates.forEach((date) => {
        sortedGrouped[date] = grouped[date];
      });

      return sortedGrouped;
    },
  },
  async mounted() {
    await this.loadAgendaData();
    await this.loadData();
  },
  methods: {
    async loadData() {
        const response = await mobileRequest({
          url: "/api/blade-system/dict-biz/dictionary",
          method: "get",
          params: {
            code: "hy_agenda", // 字典编码，需要在后台配置
          }
        });

        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
            this.mainTitle = dictData.find(
              (item) => item.dictValue === "主标题"
            )?.dictKey;
            this.subTitle = dictData.find(
              (item) => item.dictValue === "副标题"
            )?.dictKey;
            // 从字典中读取议程PDF的URL
            this.agendaPdfUrl = dictData.find(
              (item) => item.dictValue === "议程PDF"
            )?.dictKey || "";
          } else {
            console.log("API返回数据为空");
          }
        } else {
          throw new Error("API响应格式不正确");
        
      }
    },


    /**
     * 加载议程数据
     */
    async loadAgendaData() {
      const startTime = Date.now();

      try {
        console.log("开始加载议程数据...");

        // 直接调用API
        const response = await getList(1, 20, {});
        console.log("API响应:", response);

        // 检查响应格式
        if (response && response.data && response.data.success) {
          // 使用数据转换器处理数据
          const transformedData = dataTransformers.agenda(response.data);
          console.log("转换后的数据:", transformedData);

          this.agendaList = transformedData;
          this.dataSource = "api";
          this.hasError = false;
          this.errorMessage = "";
        } else {
          throw new Error("API响应格式不正确");
        }

        this.responseTime = Date.now() - startTime;
      } catch (error) {
        console.error("加载议程数据失败:", error);
        this.agendaList = this.defaultAgendaData;
        this.dataSource = "fallback";
        this.hasError = true;
        // this.errorMessage = error.message || '数据加载失败，使用默认数据';
        this.responseTime = Date.now() - startTime;
      }
    },

    /**
     * 刷新议程数据
     */
    async refreshData() {
      await this.loadAgendaData();
    },

    /**
     * 格式化议程数据
     */
    formatApiData(data, type) {
      if (type === "array" && Array.isArray(data)) {
        return dataTransformers.agenda(data);
      }
      return data;
    },

    /**
     * 下载议程文件 - 使用钉钉下载和打开
     */
    downloadAgenda() {
      // 检查是否有PDF URL
      if (!this.agendaPdfUrl) {
        if (this.$message) {
          this.$message.warning('议程PDF暂无下载链接');
        } else {
          alert('议程PDF暂无下载链接');
        }
        return;
      }

      // 获取文件名
      const fileName = '会议议程.pdf';

      // 处理URL，确保是完整的绝对路径
      let downloadUrl = this.agendaPdfUrl;
      if (downloadUrl && !downloadUrl.startsWith('http')) {
        // 如果是相对路径，转换为绝对路径
        const baseUrl = window.location.origin;
        downloadUrl = baseUrl + downloadUrl;
      }

      // 检查钉钉环境
      if (typeof dd !== 'undefined' && dd.biz && dd.biz.util && dd.biz.util.downloadFile) {
        console.log('使用钉钉JSAPI下载');
        dd.downloadFile({
          url: downloadUrl,
          name: fileName,
          onSuccess: (result) => {
            console.log('钉钉下载成功:', result);
            if (this.$message) {
              this.$message.success(`文件下载成功：${fileName}`);
            }

            // 下载成功后自动预览
            if (result.filePath) {
              dd.openDocument({
                filePath: result.filePath,
                fileType: 'pdf',
                success: (openResult) => {
                  console.log('文档预览成功:', openResult);
                },
                fail: (openErr) => {
                  console.error('文档预览失败:', openErr);
                  if (this.$message) {
                    this.$message.info(`文件已下载，可在钉钉文件中查看：${fileName}`);
                  }
                }
              });
            }
          },
          onFail: (err) => {
            console.error('钉钉下载失败:', err);
            if (this.$message) {
              this.$message.warning(`钉钉下载失败`);
            }
          }
        });
      } else {
        if (this.$message) {
          this.$message.success(`钉钉下载不可用`);
        }
      }
    },

    /**
     * 处理API测试成功
     */
    handleTestSuccess(result) {
      console.log("API测试成功:", result);
      this.responseTime = result.responseTime;
    },

    /**
     * 处理API测试错误
     */
    handleTestError(result) {
      console.error("API测试失败:", result);
      this.hasError = true;
      this.errorMessage = result.error || "API测试失败";
    },

    /**
     * 获取API测试调用函数
     * 返回一个绑定了正确上下文的函数用于API状态监控组件测试
     */
    getTestApiCall() {
      console.log("获取API测试调用函数...");
      return async () => {
        console.log("执行API测试调用...");
        try {
          const response = await getList(1, 10, {});
          console.log("API测试响应:", response);
          return response;
        } catch (error) {
          console.error("API测试调用失败:", error);
          throw error;
        }
      };
    },

    /**
     * 提取开始时间用于排序
     */
    extractStartTime(timeStr) {
      if (!timeStr) return "00:00:00";

      // 处理 "08:30:00-09:00:00" 或 "08:30-09:00" 格式
      const startTime = timeStr.split("-")[0].trim();

      // 确保时间格式为 HH:MM:SS
      if (startTime.length === 5) {
        return startTime + ":00"; // 08:30 -> 08:30:00
      }

      return startTime || "00:00:00";
    },

    /**
     * 格式化日期显示
     */
    formatDate(dateStr) {
      if (!dateStr) return "会议议程";

      try {
        const date = new Date(dateStr);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();

        // 获取所有日期并排序，动态确定是第几天
        const allDates = [
          ...new Set(this.agendaList.map((item) => item.date)),
        ].sort();
        const dayIndex = allDates.indexOf(dateStr);

        if (dayIndex >= 0) {
          const dayNumber = dayIndex + 1;
          return `第${dayNumber}天 - ${year}年${month}月${day}日`;
        } else {
          return `${year}年${month}月${day}日`;
        }
      } catch (error) {
        return dateStr;
      }
    },
  },
};
</script>

<style scoped>
/* 页面通用样式 */
.page-container {
  width: 100%;
  max-height: 85vh;
  background-size: cover;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 20px 0;
}

/* 页面内容 */
.page-content {
  margin-top: 15px;
}

/* 容器样式 - 统一磨砂玻璃效果 */
.list-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 15px;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.05),
    0 10px 25px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.6s ease forwards;
  max-width: 1400px;
  margin: 0 auto;
  max-height: 85vh;
}

/* 标题区域样式强化 */
.form-header {
  text-align: center;
  color: #ffffff;
  position: relative;
  padding: 0 20px;
}

.form-header .header-decoration {
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(7, 211, 240, 0) 0%,
    rgba(7, 211, 240, 0.6) 50%,
    rgba(7, 211, 240, 0) 100%
  );
  margin: 15px auto;
  width: 60%;
}

.form-header i {
  font-size: 42px;
  color: #07d3f0;
  margin-bottom: 15px;
  text-shadow: 0 0 15px rgba(7, 211, 240, 0.6);
  animation: pulse 3s infinite;
}

.form-header h2 {
  color: #ffffff;
  font-size: 28px;
  margin-bottom: 12px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
  padding: 0 15px;
}

.form-header h2::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 10%;
  width: 80%;
  height: 3px;
  background: rgba(7, 211, 240, 0.5);
  border-radius: 3px;
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  padding: 0 10px;
  margin-top: 20px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* 滚动容器优化 */
.agenda-scroll-container {
  max-height: calc(85vh - 310px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-right: 5px;
  margin-bottom: 10px;
  scroll-behavior: smooth;
}

/* 滚动条美化增强 */
.agenda-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.agenda-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  margin: 10px 0;
}

.agenda-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(7, 211, 240, 0.5);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.agenda-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(7, 211, 240, 0.8);
  transform: scaleX(1.2);
}

/* 列表项样式优化 - 增强层次感 */
.list-item {
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid rgba(7, 211, 240, 0.7);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease forwards;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 卡片悬停效果强化 */
.list-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.list-item:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.list-item:hover {
  transform: translateX(3px) translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.09);
  border-left-color: rgba(7, 211, 240, 0.9);
}

/* 议程标题样式 */
.agenda-title {
  color: #ffffff;
  font-size: 19px;
  margin-bottom: 15px;
  line-height: 1.5;
  position: relative;
  z-index: 2;
  padding-left: 5px;
}

/* 议程详情布局优化 */
.agenda-details {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.agenda-details p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin: 0;
  line-height: 1.6;
  padding: 6px 10px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.03);
  display: flex;
  align-items: center;
  gap: 8px;
}

.agenda-details p i {
  color: #07d3f0;
  width: 16px;
  text-align: center;
}

/* 时间标签样式优化 */
.time-badge {
  background: rgba(7, 211, 240, 0.15);
  color: #07d3f0;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: bold;
  display: inline-block;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(7, 211, 240, 0.1);
}

/* 日期标题样式增强 */
.day-section {
  margin-bottom: 40px;
  position: relative;
}

.day-title {
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.3),
    rgba(7, 211, 240, 0.15)
  );
  color: white;
  padding: 18px 25px;
  border-radius: 10px;
  margin-bottom: 15px;
  font-size: 19px;
  display: flex;
  align-items: center;
  gap: 15px;
  border: 1px solid rgba(7, 211, 240, 0.2);
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.day-title i {
  font-size: 22px;
  text-shadow: 0 0 10px rgba(7, 211, 240, 0.5);
}

.day-title .day-divider {
  flex-grow: 1;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  margin-left: 15px;
}

/* 议程分隔线 */
.agenda-divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(7, 211, 240, 0) 0%,
    rgba(7, 211, 240, 0.4) 50%,
    rgba(7, 211, 240, 0) 100%
  );
  margin-top: 15px;
  opacity: 0.7;
}

.agenda-divider.has-speaker {
  background: linear-gradient(
    90deg,
    rgba(7, 211, 240, 0) 0%,
    rgba(7, 211, 240, 0.6) 50%,
    rgba(7, 211, 240, 0) 100%
  );
}

/* 无数据状态优化 */
.empty-state .list-item {
  text-align: center;
  padding: 50px 20px;
  background: rgba(255, 255, 255, 0.04);
}

.empty-item i {
  font-size: 60px;
  color: rgba(7, 211, 240, 0.2);
  margin-bottom: 20px;
  display: block;
}

.empty-item p {
  color: rgba(255, 255, 255, 0.5);
  font-size: 16px;
}

/* 下载区域样式增强 */
.download-section {
  margin-top: 10px;
  text-align: center;
  padding: 0 20px;
}

/* 提交按钮 - 增强视觉效果 */
.submit-btn {
  width: 100%;
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.3),
    rgba(7, 211, 240, 0.15)
  );
  color: white;
  border: 1px solid rgba(7, 211, 240, 0.3);
  border-radius: 10px;
  padding: 16px;
  font-size: 17px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -150%;
  width: 300%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-20deg);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
}

.submit-btn:hover::before {
  opacity: 1;
  animation: shimmer 1.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(7, 211, 240, 0.25);
  background: linear-gradient(
    135deg,
    rgba(7, 211, 240, 0.4),
    rgba(7, 211, 240, 0.2)
  );
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 动画效果增强 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 交错动画延迟 */
.list-item:nth-child(1) {
  animation-delay: 0.1s;
}
.list-item:nth-child(2) {
  animation-delay: 0.2s;
}
.list-item:nth-child(3) {
  animation-delay: 0.3s;
}
.list-item:nth-child(4) {
  animation-delay: 0.4s;
}
.list-item:nth-child(5) {
  animation-delay: 0.5s;
}

/* 响应式调整优化 */
@media (max-width: 768px) {
  .page-container {
    padding: 15px 0;
  }

  .form-header h2 {
    font-size: 24px;
  }

  .form-header p {
    font-size: 14px;
  }

  .day-title {
    font-size: 17px;
    padding: 15px 15px;
  }

  .agenda-title {
    font-size: 17px;
  }

  .agenda-details p {
    font-size: 13px;
  }

  .submit-btn {
    padding: 14px;
    font-size: 16px;
  }

  .agenda-scroll-container {
    max-height: calc(85vh - 310px);
  }

  .empty-state .list-item {
    padding: 30px 15px;
  }

  .empty-item i {
    font-size: 40px;
  }
}

/* 超宽屏幕适配 */
@media (min-width: 1600px) {
  .list-container {
    padding: 40px;
    max-width: 1600px;
  }

  .agenda-details {
    grid-template-columns: 2fr 1fr 1fr;
  }

  .list-item {
    padding: 30px;
  }

  .agenda-title {
    font-size: 21px;
  }

  .agenda-details p {
    font-size: 15px;
  }
}
</style>
