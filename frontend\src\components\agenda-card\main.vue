<template>  
  <div class="content-column">
    <div class="scroll-container" ref="container">
      <h3 class="scroll-text" ref="textElement">{{ this.text }}</h3>
    </div>
  </div>
</template>

<script>
export default {
  data(){
    return {
      text:"当前议程"
    }
  },
  mounted() {
    // 检查文本是否需要滚动
    this.checkIfNeedsScroll();
    
    // 监听窗口大小变化，重新检查
    window.addEventListener('resize', this.checkIfNeedsScroll);
  },
  beforeUnmount() {
    // 移除事件监听
    window.removeEventListener('resize', this.checkIfNeedsScroll);
  },
  methods: {
    checkIfNeedsScroll() {
      const container = this.$refs.container;
      const textElement = this.$refs.textElement;
      
      if (!container || !textElement) return;
      
      // 获取容器和文本的宽度
      const containerWidth = container.offsetWidth;
      const textWidth = textElement.offsetWidth;
      
      // 如果文本宽度大于容器宽度，则添加滚动动画类
      if (textWidth > containerWidth) {
        textElement.classList.add('scroll-animation');
      } else {
        textElement.classList.remove('scroll-animation');
      }
    }
  }
};
</script>

<style scoped>
/* 内容容器样式 */
.content-column {
  width: 100%;
  padding: 4vw 6vw;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  
  /* 保持18%屏幕高度并居中显示 */
  max-height: 18vh;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

/* 滚动容器 */
.scroll-container {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
}

/* 议程名称 */
.scroll-text {
  color: #ffffff;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  margin: 0;
  font-size: 1.1rem;
  letter-spacing: 0.3px;
  display: inline-block;
  white-space: nowrap;
}

/* 滚动动画类 - 仅在需要时添加 */
.scroll-text.scroll-animation {
  animation: scroll 50s linear infinite;
  padding-left: 100%;
}

/* 滚动动画 */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 响应式调整 */
@media (max-width: 320px) {
  .content-column {
    height: 25vh;
    min-height: 140px;
    padding: 3vw 4vw;
  }
  
  .scroll-text {
    font-size: 0.85rem;
  }
}

@media (min-width: 480px) {
  .scroll-text {
    font-size: 1rem;
  }
}
</style>
