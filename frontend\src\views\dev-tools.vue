<template>
  <div class="dev-tools">
    <h1>🛠️ 开发工具</h1>
    <p class="subtitle">仅在开发环境中可用的测试工具</p>
    
    <div class="tools-grid">
      <div class="tool-card" @click="goToMobileAuthTest">
        <div class="tool-icon">📱</div>
        <h3>Mobile登录守卫测试</h3>
        <p>测试mobile页面的登录守卫功能</p>
        <button class="tool-btn">进入测试</button>
      </div>
      
      <div class="tool-card" @click="goToMobile">
        <div class="tool-icon">📲</div>
        <h3>Mobile页面</h3>
        <p>直接访问mobile页面</p>
        <button class="tool-btn">访问页面</button>
      </div>
      
      <div class="tool-card" @click="openConsoleTools">
        <div class="tool-icon">🔧</div>
        <h3>控制台工具</h3>
        <p>在控制台中加载测试工具</p>
        <button class="tool-btn">加载工具</button>
      </div>
      
      <div class="tool-card" @click="clearStorage">
        <div class="tool-icon">🗑️</div>
        <h3>清除存储</h3>
        <p>清除localStorage和sessionStorage</p>
        <button class="tool-btn">清除数据</button>
      </div>
    </div>
    
    <div class="info-section">
      <h3>📋 使用说明</h3>
      <ul>
        <li><strong>Mobile登录守卫测试</strong>：测试mobile页面的登录保护功能</li>
        <li><strong>Mobile页面</strong>：直接访问mobile页面，会触发登录守卫</li>
        <li><strong>控制台工具</strong>：在浏览器控制台中加载测试工具函数</li>
        <li><strong>清除存储</strong>：清除所有本地存储数据，模拟全新用户</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DevTools',
  methods: {
    goToMobileAuthTest() {
      // this.$router.push('/mobile-auth-test');
    },
    
    goToMobile() {
      this.$router.push('/mobile');
    },
    
    async openConsoleTools() {
      // try {
      //   // 动态导入测试工具
      //   await import('@/utils/mobile-auth-test.js');
      //   console.log('✅ 测试工具已加载到控制台');
      //   console.log('可用命令:');
      //   console.log('- mobileAuthTest.login() // 模拟登录');
      //   console.log('- mobileAuthTest.logout() // 模拟登出');
      //   console.log('- mobileAuthTest.check() // 检查登录状态');
      //   console.log('- mobileAuthTest.test() // 运行完整测试');
      //
      //   alert('测试工具已加载到控制台，请打开开发者工具查看可用命令');
      // } catch (error) {
      //   console.error('加载测试工具失败:', error);
      //   alert('加载测试工具失败，请检查控制台');
      // }
    },
    
    clearStorage() {
      if (confirm('确定要清除所有本地存储数据吗？这将模拟全新用户状态。')) {
        localStorage.clear();
        sessionStorage.clear();
        console.log('✅ 本地存储已清除');
        alert('本地存储已清除，页面将刷新');
        window.location.reload();
      }
    }
  }
}
</script>

<style scoped>
.dev-tools {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 40px;
  font-style: italic;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.tool-card {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tool-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border-color: #007bff;
}

.tool-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.tool-card h3 {
  color: #333;
  margin-bottom: 12px;
  font-size: 18px;
}

.tool-card p {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.tool-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.tool-btn:hover {
  background: #0056b3;
}

.info-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
}

.info-section h3 {
  color: #333;
  margin-bottom: 16px;
}

.info-section ul {
  list-style: none;
  padding: 0;
}

.info-section li {
  margin-bottom: 12px;
  padding-left: 20px;
  position: relative;
}

.info-section li::before {
  content: "•";
  color: #007bff;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.info-section strong {
  color: #333;
}
</style>
